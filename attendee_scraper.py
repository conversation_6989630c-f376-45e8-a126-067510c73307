import requests
import csv
import time
import json
import random
import os

BASE_URL = 'https://globalfinance.connectmeinforma.com/api/v1/attendee-guest'
LIMIT = 50
CSV_FILENAME = 'attendees_event270.csv'
CHECKPOINT_FILE = 'scraper_checkpoint_event270.txt'

HEADERS = {
    'accept': '*/*',
    'accept-language': 'en-IE,en-GB;q=0.9,en-US;q=0.8,en;q=0.7',
    'access-control-allow-origin': 'http://localhost:8080',
    'cache-control': 'no-cache',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://globalfinance.connectmeinforma.com/270/attendees',
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}

# It's crucial to get the cookie string correct.
# This string might expire or change.
COOKIE_STRING = """
_gcl_au=1.1.974125323.1749904776; _ga=GA1.1.2146361950.1749904776; __td_signed=true; sp=1193f15f-3559-4100-a62e-8a3adc4ba1d2; __act=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.c62Ed_6WR4aLIx_bNYzX6S4Z-d_ZbJYhCCE8Gpl94Bw; _reef_at=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjE1NzYwNSwidGVuYW50SWQiOiJ0X1J5VDg2UG8xVTh2N01sU2NKUWpRSSIsInNjb3BlIjoiZ29kIiwiaWF0IjoxNzUwNDEzMjQ2LCJleHAiOjE3NTA0MTQ0NDZ9.MZoBKpI9DxaufHjCKt4QN0xIDiKNqbwnXkhwltA_P80; _reef_rt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjE1NzYwNSwidGVuYW50SWQiOiJ0X1J5VDg2UG8xVTh2N01sU2NKUWpRSSIsInNjb3BlIjoiZ29kIiwiaWF0IjoxNzUwNDEzMjQ2LCJleHAiOjE3NTEwMTgwNDZ9.w3WmYfP7qCxJQnlfxod8drl4T6348bhvpJIGKnaNZI4; __rt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************.3eXj80ltsbWu7qbG7OAVJP7jbzd8g10WGIUEkvqD2aM; _iris_duid=563a8c2a-71c0-45de-a034-854773d24914; _sp_ses.4a60=*; crisp-client%2Fsession%2Ff747dc6f-c233-4683-8635-37519dd91a94=session_5b37d0a4-e1fd-401c-938d-3cd0af160f16; _ga_68MJSJ929S=GS2.1.s1750413247$o3$g1$t1750413254$j53$l0$h0; _ga_NZ1RN4J8JG=GS2.1.s1750413247$o3$g1$t1750413255$j52$l0$h0; _td=ba06fac1-9ec2-4634-9500-b77b3d4ac33a; _sp_id.4a60=563a8c2a-71c0-45de-a034-854773d24914.1749904776.3.1750413255.1749912612.b79a2c87-88f4-4f9b-a4ad-3db389c9fdc5.3a5a61f7-697d-49ed-977f-c73917931d71.61f53d31-698b-42d0-81d5-cf992b350296.1750413247754.4
"""
# Convert cookie string to a dictionary for requests
cookies = {cookie.split('=')[0]: cookie.split('=')[1] for cookie in COOKIE_STRING.strip().split('; ')}

def load_checkpoint():
    if os.path.exists(CHECKPOINT_FILE):
        try:
            with open(CHECKPOINT_FILE, 'r') as f:
                offset = int(f.read().strip())
                print(f"Resuming from checkpoint. Offset: {offset}")
                return offset
        except ValueError:
            print(f"Warning: Invalid content in {CHECKPOINT_FILE}. Starting from offset 0.")
        except Exception as e:
            print(f"Warning: Could not read {CHECKPOINT_FILE}: {e}. Starting from offset 0.")
    return 0

def save_checkpoint(offset):
    try:
        with open(CHECKPOINT_FILE, 'w') as f:
            f.write(str(offset))
        # print(f"Checkpoint saved: next offset is {offset}") # Verbose, consider removing if too noisy
    except Exception as e:
        print(f"Error saving checkpoint to {CHECKPOINT_FILE}: {e}")

def get_csv_fieldnames_from_file(filename):
    try:
        with open(filename, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile)
            headers = next(reader)
            if headers:
                # print(f"Read headers from existing CSV: {headers}") # Verbose
                return headers
    except StopIteration:
        print(f"CSV file {filename} is empty. Headers will be determined from data.")
    except FileNotFoundError:
        # This case is fine, means we are creating the file.
        pass
    except Exception as e:
        print(f"Error reading headers from {filename}: {e}. Headers will be determined from data.")
    return None

def fetch_attendees():
    session_attendees_count = 0
    offset = load_checkpoint()
    total_api_count = None # Added to store total count from API

    csv_fieldnames = None
    can_read_existing_headers = False

    if os.path.exists(CSV_FILENAME) and os.path.getsize(CSV_FILENAME) > 0:
        temp_fieldnames = get_csv_fieldnames_from_file(CSV_FILENAME)
        if temp_fieldnames:
            csv_fieldnames = temp_fieldnames
            can_read_existing_headers = True
            print(f"Successfully read headers from existing {CSV_FILENAME}")
        else:
            print(f"Warning: {CSV_FILENAME} exists and is not empty, but could not read/parse headers.")

    if offset > 0 and can_read_existing_headers:
        file_mode = 'a'
        write_header_flag = False
        print(f"Resuming. Appending to {CSV_FILENAME}. Headers assumed to exist and match based on prior read.")
    else:
        file_mode = 'w'
        write_header_flag = True
        # csv_fieldnames will be determined by the first batch of data if not already set (e.g. from failed header read)
        if os.path.exists(CSV_FILENAME) and offset == 0 and not can_read_existing_headers :
             print(f"Starting fresh. {CSV_FILENAME} will be overwritten (or was empty/headers unreadable).")
        elif not os.path.exists(CSV_FILENAME):
             print(f"Starting fresh. {CSV_FILENAME} will be created.")
        elif offset > 0 and not can_read_existing_headers:
            print(f"Warning: Resuming (offset={offset}) but could not read headers from existing {CSV_FILENAME}. File will be overwritten to ensure header consistency.")

        if file_mode == 'w' and csv_fieldnames and can_read_existing_headers:
            # This case means we decided to overwrite even though we could read headers, likely due to offset=0
            print(f"Note: Overwriting {CSV_FILENAME}, previously read headers will be used if first batch is empty.")
        elif file_mode == 'w':
             csv_fieldnames = None # Ensure it's determined from first batch


    print(f"Effective mode: '{file_mode}', Initial offset: {offset}, Will write header: {write_header_flag}")

    with open(CSV_FILENAME, file_mode, newline='', encoding='utf-8') as csvfile:
        csv_writer = None

        while True:
            params = {'limit': LIMIT, 'offset': offset}
            print(f"Fetching attendees: offset={offset}, limit={LIMIT}")

            try:
                response = requests.get(BASE_URL, headers=HEADERS, params=params, cookies=cookies)
                response.raise_for_status()

                data = response.json()
                attendees = data.get("rows", [])

                if total_api_count is None and "count" in data:
                    try:
                        total_api_count = int(data["count"])
                        print(f"API indicates a total of {total_api_count} attendees.")
                    except ValueError:
                        print(f"Warning: Could not parse total count from API: {data['count']}")
                        total_api_count = 0 # Or handle as error / unknown

                if not attendees:
                    print("No more attendees found or empty response. Process finished.")
                    save_checkpoint(offset) # Save final offset indicating completion.
                    break

                if csv_writer is None:
                    if not csv_fieldnames: # Not read from existing file, or 'w' mode forced re-evaluation
                        if attendees: # Ensure attendees is not empty before accessing attendees[0]
                            csv_fieldnames = list(attendees[0].keys())
                            print(f"Determined CSV fieldnames from first fetched batch: {csv_fieldnames}")
                        else: # Should be caught by 'if not attendees:' above, but as a safeguard.
                            print("CRITICAL: No attendees in first batch and no headers. Cannot proceed.")
                            return # Exit fetch_attendees

                    if not csv_fieldnames: # Final check
                         print("CRITICAL: CSV fieldnames could not be determined. Aborting.")
                         return

                    csv_writer = csv.DictWriter(csvfile, fieldnames=csv_fieldnames, extrasaction='ignore')
                    if write_header_flag:
                        print(f"Writing CSV header: {csv_fieldnames}")
                        csv_writer.writeheader()
                        write_header_flag = False

                current_batch_count = 0
                for attendee in attendees:
                    processed_attendee = {}
                    for key, value in attendee.items():
                        if isinstance(value, list):
                            processed_attendee[key] = ', '.join(map(str, value))
                        elif value is None:
                            processed_attendee[key] = ''
                        else:
                            processed_attendee[key] = value

                    # Ensure only known fields are written by relying on DictWriter's fieldnames and extrasaction='ignore'
                    csv_writer.writerow(processed_attendee)
                    current_batch_count +=1

                session_attendees_count += current_batch_count
                csvfile.flush()

                progress_message = f"Fetched and wrote {current_batch_count} attendees. Total this session: {session_attendees_count}."
                if total_api_count is not None and total_api_count > 0:
                    # Offset is where the *next* batch will start, so current processed is offset + current_batch_count (or just offset if thinking about start of this batch)
                    # Let's use 'offset' as it reflects the number of records processed before the *next* fetch attempt.
                    # If a batch of 50 was just fetched starting at offset 0, offset becomes 50 for the next round.
                    # So, 'offset' (after increment) represents the total processed so far if we just completed the batch that started at old 'offset'.
                    # current_total_processed_according_to_offset = offset + LIMIT # This is the next offset
                    # Let's use a simpler approach: current number of records on disk if resuming, or session count if starting fresh.
                    # For simplicity and clarity with checkpointing, let's base progress on the current offset.
                    # The 'offset' variable holds the starting point for the *current* batch that was just processed.
                    # So (offset + current_batch_count) is the total number of records processed up to the end of the current batch.
                    num_processed_so_far = offset + current_batch_count
                    percentage = (num_processed_so_far / total_api_count) * 100
                    progress_message += f" Progress: {num_processed_so_far}/{total_api_count} ({percentage:.2f}%)."
                print(progress_message)

                save_checkpoint(offset + LIMIT) # Save checkpoint for the *next* iteration's offset

                offset += LIMIT

                sleep_duration = random.uniform(3, 10)
                print(f"Sleeping for {sleep_duration:.2f} seconds...")
                time.sleep(sleep_duration)

            except requests.exceptions.HTTPError as http_err:
                print(f"HTTP error occurred: {http_err} - Status: {response.status_code if 'response' in locals() else 'N/A'}")
                if 'response' in locals() and response is not None:
                    print(f"Response text: {response.text[:500]}...")
                try:
                    if 'response' in locals() and response is not None:
                        error_data = response.json()
                        print(f"Error details from JSON: {error_data}")
                except json.JSONDecodeError:
                    print("Could not parse error response as JSON.")
                print(f"Stopping due to HTTP error. Last successful offset processed was before {offset}. Next run will attempt to restart from checkpoint {CHECKPOINT_FILE}.")
                break
            except requests.exceptions.RequestException as req_err:
                print(f"Request error occurred: {req_err}. Last offset attempted: {offset}.")
                break
            except json.JSONDecodeError as json_err:
                print(f"Failed to decode JSON response: {json_err}. Last offset attempted: {offset}.")
                if 'response' in locals() and response is not None:
                     print(f"Response text: {response.text[:500]}...")
                break
            except Exception as e:
                print(f"An unexpected error occurred: {e}. Last offset attempted: {offset}.")
                break

    print(f"Script finished or interrupted. Total attendees written this session: {session_attendees_count}")

if __name__ == "__main__":
    fetch_attendees()
    print("Attendee fetching process has concluded.")
