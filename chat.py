import argparse
import csv
import json
import logging
import os
import random
import sys
import time

import requests


def _get_short_id(full_id: str, prefix: str) -> str:
    """Extracts the short ID from a full ID string like 'prefix-shortid'."""
    prefix_dash = f"{prefix}-"
    if full_id.startswith(prefix_dash):
        return full_id[len(prefix_dash):]
    # Fallback or error if format is unexpected, for now, assume it might be a short ID already
    # Or raise ValueError(f"Full ID '{full_id}' does not match prefix '{prefix}-'")
    logging.warning(f"Full ID '{full_id}' did not start with prefix '{prefix}-'. Using full ID as short ID.")
    return full_id


def _generate_channel_details(prefix: str, event_id: str, user1_full_id: str, user2_full_id: str) -> tuple[str, list[str]]:
    """
    Generates the canonical channel ID and the list of full member IDs.
    The channel ID is constructed using sorted short IDs.
    """
    user1_short_id = _get_short_id(user1_full_id, prefix)
    user2_short_id = _get_short_id(user2_full_id, prefix)

    sorted_short_ids = sorted([user1_short_id, user2_short_id])

    channel_id = f"{prefix}-{event_id}-{sorted_short_ids[0]}-{sorted_short_ids[1]}"
    # The members list for the /query payload generally expects full IDs.
    # Order might matter for some backends, but often doesn't. Using the original full IDs.
    members = sorted([user1_full_id, user2_full_id]) # Ensure members are consistently ordered for payload
    return channel_id, members


def _ensure_channel_exists(base_url: str, api_key: str, user_token: str,
                           channel_type: str, channel_id: str,
                           querying_user_full_id: str, members_full_ids: list[str], team_id: str,
                           connection_id: str) -> bool:
    """
    Ensures a channel exists by calling the /query endpoint.
    Creates the channel if it doesn't exist.
    """
    url = f"{base_url}/channels/{channel_type}/{channel_id}/query"
    params = {
        'api_key': api_key,
        'user_id': querying_user_full_id  # The user performing the query
    }
    if connection_id: # Only add connection_id if provided, as watch is True
        params['connection_id'] = connection_id

    headers = {
        'Content-Type': 'application/json',
        'Stream-Auth-Type': 'jwt',
        'Authorization': user_token
    }
    payload = {
        "data": {
            "members": members_full_ids,
            "team": team_id
            # Add any other necessary fields for channel creation if required
            # e.g., "name": f"Chat between {members_full_ids[0]} and {members_full_ids[1]}"
        },
        "state": True,  # Request channel state in response
        "watch": True,  # The querying user will watch the channel
        "presence": False
    }

    logging.info(f"Ensuring channel {channel_id} exists with members {members_full_ids} and team {team_id}.")
    try:
        response = requests.post(url, params=params, headers=headers, json=payload)
        if response.ok:
            logging.info(f"Channel {channel_id} query/create successful (status {response.status_code}).")
            return True
        else:
            logging.error(f"Failed to query/create channel {channel_id} (status {response.status_code}): {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        logging.error(f"Request failed during channel query/create for {channel_id}: {e}")
        return False


def _check_recipient_has_sent_messages(base_url: str, api_key: str, user_token: str,
                                     channel_type: str, channel_id: str,
                                     querying_user_full_id: str, recipient_full_id: str) -> bool:
    """
    Checks if the recipient has sent any messages in the channel.
    Returns True if recipient has sent messages, False otherwise.
    """
    url = f"{base_url}/channels/{channel_type}/{channel_id}/messages"
    params = {
        'api_key': api_key,
        'user_id': querying_user_full_id,
        'limit': 100,  # Check recent messages
        'user_id_filter': recipient_full_id  # Filter messages by recipient
    }

    headers = {
        'Content-Type': 'application/json',
        'Stream-Auth-Type': 'jwt',
        'Authorization': user_token
    }

    logging.info(f"Checking if {recipient_full_id} has sent any messages in channel {channel_id}.")
    try:
        response = requests.get(url, params=params, headers=headers)
        if response.ok:
            data = response.json()
            messages = data.get('messages', [])

            # Filter messages sent by the recipient
            recipient_messages = [msg for msg in messages if msg.get('user', {}).get('id') == recipient_full_id]

            has_sent_messages = len(recipient_messages) > 0
            logging.info(f"Recipient {recipient_full_id} has sent {len(recipient_messages)} messages in channel {channel_id}.")
            return has_sent_messages
        else:
            logging.error(f"Failed to check messages for {recipient_full_id} in channel {channel_id} (status {response.status_code}): {response.text}")
            return False  # Default to False to allow sending if API call fails
    except requests.exceptions.RequestException as e:
        logging.error(f"Request failed during message check for {recipient_full_id} in {channel_id}: {e}")
        return False  # Default to False to allow sending if API call fails


def load_state(state_path):
    if os.path.isfile(state_path):
        with open(state_path, 'r') as f:
            return json.load(f)
    return {"last_index": -1}


def save_state(state_path, state):
    temp_state_path = state_path + ".tmp"
    try:
        with open(temp_state_path, 'w') as f:
            json.dump(state, f, indent=2)
        os.rename(temp_state_path, state_path)
    except Exception as e:
        logging.error(f"Failed to save state to {state_path}: {e}")
        if os.path.exists(temp_state_path):
            try:
                os.remove(temp_state_path)
            except Exception as e_remove:
                logging.error(f"Failed to remove temporary state file {temp_state_path}: {e_remove}")


def send_message(base_url, api_key, user_token, channel_type, channel_id, sender_id, recipient_id, text):
    url = f"{base_url}/channels/{channel_type}/{channel_id}/message"
    params = {
        'api_key': api_key,
        'user_id': sender_id
    }
    headers = {
        'Content-Type': 'application/json',
        'Stream-Auth-Type': 'jwt',
        'Authorization': user_token
    }
    payload = {"message": {"text": text}}
    response = requests.post(url, params=params, headers=headers, json=payload)
    return response


def main():
    parser = argparse.ArgumentParser(description="Send messages from a CSV list to individual channels.")
    parser.add_argument('--csv-file', required=True, help='Path to CSV file with columns: user_id,message')
    parser.add_argument('--sender-id', required=True, help='Sender user ID')
    parser.add_argument('--api-key', required=True, help='Stream API key')
    parser.add_argument('--user-token', required=True, help='JWT for sender user')
    parser.add_argument('--event-id', required=True, help='Event ID to include in channel names')
    parser.add_argument('--prefix', default='gfin-informa', help='Channel prefix (default: gfin-informa)')
    parser.add_argument('--base-url', default='https://chat.stream-chat.com', help='Stream base URL')
    parser.add_argument('--limit', type=int, help='Limit the number of messages to send (for testing)')
    parser.add_argument('--override-recipient-id', help='Override recipient ID for sending a single test message from the CSV (first row message column)')
    parser.add_argument('--message-column-name', default='message', help='Name of the CSV column containing the message text (default: message)')
    parser.add_argument('--connection-id', help='Active websocket connection ID for the sender (required if watching channels)')
    parser.add_argument('--filter-no-response', action='store_true', help='Only send message if recipient has not sent any messages in the channel')
    args = parser.parse_args()

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    state_path = f"{args.csv_file}.state.json"
    state = load_state(state_path)
    last_index = state.get('last_index', -1)

    with open(args.csv_file, newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        rows = list(reader)

    total = len(rows)

    if args.override_recipient_id:
        if not rows:
            logging.error("CSV file is empty. Cannot send test message.")
            sys.exit(1)

        first_row = rows[0]
        text = first_row.get(args.message_column_name)
        test_recipient_full_name = first_row.get('full_name', 'N/A') # Get full_name for logging

        if not text:
            logging.error(f"First row in CSV does not have a '{args.message_column_name}' column or it's empty.")
            sys.exit(1)

        raw_test_recipient_id = args.override_recipient_id
        sender_full_id = args.sender_id # Assumed to be full ID from CLI

        # Ensure test_recipient_full_id is a full ID
        if not raw_test_recipient_id.startswith(f"{args.prefix}-"):
            test_recipient_full_id = f"{args.prefix}-{raw_test_recipient_id}"
            logging.info(f"Override recipient ID '{raw_test_recipient_id}' appears to be a short ID. Using prefixed ID: '{test_recipient_full_id}'.")
        else:
            test_recipient_full_id = raw_test_recipient_id

        channel_id, members_list = _generate_channel_details(
            args.prefix, args.event_id, sender_full_id, test_recipient_full_id
        )
        team_id = f"{args.prefix}-{args.event_id}"

        logging.info(f"Sending TEST message to overridden recipient {test_recipient_full_id} (Full Name: {test_recipient_full_name}) on channel {channel_id} using message from first CSV row.")

        if not _ensure_channel_exists(args.base_url, args.api_key, args.user_token, 'messaging',
                                      channel_id, sender_full_id, members_list, team_id,
                                      args.connection_id):
            logging.error(f"Failed to ensure channel {channel_id} for test message to {test_recipient_full_id} (Full Name: {test_recipient_full_name}). Exiting.")
            sys.exit(1)

        # Check if recipient has sent messages (if filter is enabled)
        if args.filter_no_response:
            has_sent_messages = _check_recipient_has_sent_messages(
                args.base_url, args.api_key, args.user_token, 'messaging',
                channel_id, sender_full_id, test_recipient_full_id
            )
            if has_sent_messages:
                logging.info(f"SKIPPING test message to {test_recipient_full_id} (Full Name: {test_recipient_full_name}) - recipient has already sent messages.")
                sys.exit(0)

        response = send_message(
            args.base_url, args.api_key, args.user_token,
            'messaging', channel_id, sender_full_id, test_recipient_full_id, text # recipient_id here is mostly for logging/context
        )
        if response.ok:
            logging.info(f"Test message sent to {test_recipient_full_id} (Full Name: {test_recipient_full_name}) (status {response.status_code}).")
        else:
            logging.error(f"Failed to send test message to {test_recipient_full_id} (Full Name: {test_recipient_full_name}) (status {response.status_code}): {response.text}")
            sys.exit(1)
        sys.exit(0)

    logging.info(f"Loaded {total} rows from {args.csv_file}. Resuming from index {last_index + 1}.")

    messages_sent_count = 0
    for idx in range(last_index + 1, total):
        if args.limit is not None and messages_sent_count >= args.limit:
            logging.info(f"Reached message limit of {args.limit}. Processing stopped.")
            break

        row = rows[idx]
        recipient_short_id_from_csv = row.get('user_id') # This is the short ID e.g. "22566"
        text = row.get(args.message_column_name)
        full_name = row.get('full_name', 'N/A') # Get full_name for logging

        sender_full_id = args.sender_id # This is the full ID e.g. "gfin-informa-157606"

        if not recipient_short_id_from_csv or not text: # Check recipient_short_id_from_csv
            logging.warning(f"Skipping row {idx}: missing user_id or {args.message_column_name}. Recipient Short ID: {recipient_short_id_from_csv or 'N/A'}, Full Name: {full_name}.")
            state['last_index'] = idx
            save_state(state_path, state)
            continue

        # Construct the full recipient ID using the prefix and the short ID from CSV
        recipient_full_id = f"{args.prefix}-{recipient_short_id_from_csv}"

        channel_id, members_list = _generate_channel_details(
            args.prefix, args.event_id, sender_full_id, recipient_full_id
        )
        team_id = f"{args.prefix}-{args.event_id}"

        logging.info(f"Processing row {idx}/{total-1}: Recipient {recipient_full_id} (Full Name: {full_name}), Channel {channel_id}")

        # Ensure channel exists before attempting to send a message
        if not _ensure_channel_exists(args.base_url, args.api_key, args.user_token, 'messaging',
                                      channel_id, sender_full_id, members_list, team_id,
                                      args.connection_id):
            logging.warning(f"Failed to ensure channel {channel_id} for recipient {recipient_full_id} (Full Name: {full_name}). Skipping row {idx}.")
            state['last_index'] = idx # Mark as processed to not retry indefinitely if channel creation is the issue
            save_state(state_path, state)
            # Optional: Add a short delay if this failure might be transient and worth a retry on next script run
            # time.sleep(5)
            continue

        # Check if recipient has sent messages (if filter is enabled)
        if args.filter_no_response:
            has_sent_messages = _check_recipient_has_sent_messages(
                args.base_url, args.api_key, args.user_token, 'messaging',
                channel_id, sender_full_id, recipient_full_id
            )
            if has_sent_messages:
                logging.info(f"SKIPPING message to {recipient_full_id} (Full Name: {full_name}) - recipient has already sent messages.")
                state['last_index'] = idx
                save_state(state_path, state)
                continue

        logging.info(f"Sending to {recipient_full_id} (Full Name: {full_name}) on channel {channel_id} (row {idx}/{total-1})")

        success = False
        for attempt in range(1, 4):
            response = send_message(
                args.base_url, args.api_key, args.user_token,
                'messaging', channel_id, sender_full_id, recipient_full_id, text
            )
            if response.ok:
                logging.info(f"Message sent to {recipient_full_id} (Full Name: {full_name}) (status {response.status_code}).")
                success = True
                messages_sent_count += 1
                break
            else:
                logging.warning(
                    f"Attempt {attempt} failed for {recipient_full_id} (Full Name: {full_name}) (status {response.status_code}): {response.text}" )
                if attempt < 3:
                    logging.info("Backing off for 20 seconds before retry.")
                    time.sleep(20)
        if not success:
            logging.error(f"Failed to send to {recipient_full_id} (Full Name: {full_name}) after 3 attempts. Saving current progress and exiting.")
            save_state(state_path, state)
            sys.exit(1)

        # update state and persist
        state['last_index'] = idx
        save_state(state_path, state)

        # random delay between 20 and 40 seconds
        delay = random.uniform(7, 15)
        logging.info(f"Sleeping for {delay:.1f} seconds before next message.")
        time.sleep(delay)

    logging.info("Message processing loop finished.")
    if args.limit is not None and messages_sent_count >= args.limit:
        logging.info(f"Finished sending {messages_sent_count} messages due to limit.")
    elif (last_index + 1) >= total:
        logging.info("All messages in the CSV have been processed.")
    else:
        logging.info(f"Processed up to index {state.get('last_index', -1)}.")


if __name__ == '__main__':
    main()
