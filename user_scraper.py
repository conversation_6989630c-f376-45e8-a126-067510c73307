import requests
import csv
import time
import json
import random
import os
from urllib.parse import urlparse, urlencode

# --- Configuration ---
EVENT_ID = '265' # From cURL command referer and cookie
BASE_URL = 'https://globalfinance.connectmeinforma.com/api/v1/users'

# Files for state and output
RESULTS_CSV = f'user_results_event{EVENT_ID}.csv'
CHECKPOINT_FILE = f'user_scraper_checkpoint_event{EVENT_ID}.txt'

# Pagination parameters
LIMIT = 50

# Binary search parameters - REMOVED
# BINARY_SEARCH_INITIAL_LOW = 0
# Adjusted initial high based on typical user ID ranges, can be tuned
# BINARY_SEARCH_MAX_INITIAL_HIGH = 100000
# NUM_BATCHES = 100 # Removed
REQUEST_TIMEOUT = 20 # Seconds
AVERAGE_REQUEST_DELAY = 7.5 # Average of random.uniform(5, 10) seconds

# --- Stealth Configuration ---
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Safari/605.1.15",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
]
MAX_RETRIES_ON_RATE_LIMIT = 3
INITIAL_BACKOFF_SECONDS = 30 # Seconds to wait after first 429, doubles each retry

# Headers from cURL, Referer will be updated with EVENT_ID
HEADERS = {
    'accept': '*/*',
    'accept-language': 'en-IE,en-GB;q=0.9,en-US;q=0.8,en;q=0.7',
    'access-control-allow-origin': 'http://localhost:8080', # This might not be needed by requests
    'cache-control': 'no-cache',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    # Referer will be set dynamically
    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    # 'user-agent' will be set dynamically from USER_AGENTS
}

# Cookie string from cURL
# Ensure this is a fresh and valid cookie when running the script.
COOKIE_STRING = """
_gcl_au=1.1.974125323.1749904776; _ga=GA1.1.2146361950.1749904776; __td_signed=true; sp=1193f15f-3559-4100-a62e-8a3adc4ba1d2; __act=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.c62Ed_6WR4aLIx_bNYzX6S4Z-d_ZbJYhCCE8Gpl94Bw; _reef_at=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjE1NzYwNSwidGVuYW50SWQiOiJ0X1J5VDg2UG8xVTh2N01sU2NKUWpRSSIsInNjb3BlIjoiZ29kIiwiaWF0IjoxNzUwNDEzMjQ2LCJleHAiOjE3NTA0MTQ0NDZ9.MZoBKpI9DxaufHjCKt4QN0xIDiKNqbwnXkhwltA_P80; _reef_rt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjE1NzYwNSwidGVuYW50SWQiOiJ0X1J5VDg2UG8xVTh2N01sU2NKUWpRSSIsInNjb3BlIjoiZ29kIiwiaWF0IjoxNzUwNDEzMjQ2LCJleHAiOjE3NTEwMTgwNDZ9.w3WmYfP7qCxJQnlfxod8drl4T6348bhvpJIGKnaNZI4; __rt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************.3eXj80ltsbWu7qbG7OAVJP7jbzd8g10WGIUEkvqD2aM; _iris_duid=563a8c2a-71c0-45de-a034-854773d24914; _sp_ses.4a60=*; crisp-client%2Fsession%2Ff747dc6f-c233-4683-8635-37519dd91a94=session_5b37d0a4-e1fd-401c-938d-3cd0af160f16; _ga_68MJSJ929S=GS2.1.s1750413247$o3$g1$t1750413254$j53$l0$h0; _ga_NZ1RN4J8JG=GS2.1.s1750413247$o3$g1$t1750413255$j52$l0$h0; _td=ba06fac1-9ec2-4634-9500-b77b3d4ac33a; _sp_id.4a60=563a8c2a-71c0-45de-a034-854773d24914.1749904776.3.1750413255.1749912612.b79a2c87-88f4-4f9b-a4ad-3db389c9fdc5.3a5a61f7-697d-49ed-977f-c73917931d71.61f53d31-698b-42d0-81d5-cf992b350296.1750413247754.4
"""
# --- Helper Functions ---

def parse_cookies(cookie_str):
    """Converts a browser cookie string into a dictionary for requests."""
    cookies = {}
    if cookie_str:
        for item in cookie_str.strip().split(';'):
            item = item.strip()
            if not item:
                continue
            parts = item.split('=', 1)
            if len(parts) == 2:
                cookies[parts[0]] = parts[1]
            else: # Handle cookies without a value part, if any
                cookies[parts[0]] = ""
    return cookies

def format_time(seconds):
    """Converts seconds to HH:MM:SS string format."""
    if not isinstance(seconds, (int, float)) or seconds < 0:
        return "N/A"
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    return f"{hours:02d}:{minutes:02d}:{secs:02d}"

def get_csv_fieldnames_from_file(filename):
    """Reads existing headers from a CSV file."""
    try:
        with open(filename, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile)
            headers = next(reader)
            if headers:
                return headers
    except StopIteration: # File is empty
        pass
    except FileNotFoundError:
        pass
    except Exception as e:
        print(f"Warning: Error reading headers from {filename}: {e}")
    return None

def preprocess_data_for_csv(data_dict):
    """Prepares a dictionary for CSV writing: converts lists to strings, None to empty."""
    processed = {}
    if not isinstance(data_dict, dict): # Ensure it's a dict
        return {'error': str(data_dict)}

    for key, value in data_dict.items():
        if isinstance(value, list):
            processed[key] = ', '.join(map(str, value))
        elif isinstance(value, dict): # Flatten simple dicts or json dump complex ones
            processed[key] = json.dumps(value)
        elif value is None:
            processed[key] = ''
        else:
            processed[key] = value
    return processed

_csv_writer_info = {} # Cache for CSV writer and fieldnames: {filename: {'writer': writer, 'fieldnames': []}}

def write_to_csv(data_dict, csv_filename):
    """Writes a dictionary to a CSV file, managing headers."""
    global _csv_writer_info

    processed_data = preprocess_data_for_csv(data_dict)

    if csv_filename not in _csv_writer_info:
        _csv_writer_info[csv_filename] = {'writer': None, 'fieldnames': None}

    writer_info = _csv_writer_info[csv_filename]

    try:
        # Determine fieldnames if not already set
        if writer_info['fieldnames'] is None:
            # If file exists and has headers, use them
            existing_headers = get_csv_fieldnames_from_file(csv_filename)
            if existing_headers:
                writer_info['fieldnames'] = existing_headers
            elif processed_data: # Otherwise, use keys from current data
                writer_info['fieldnames'] = list(processed_data.keys())
            else: # No data to infer headers from, cannot proceed with writing
                print(f"Warning: No data to infer CSV headers for {csv_filename} and file is new/empty.")
                return

        if not writer_info['fieldnames']: # Still no fieldnames
             print(f"Error: CSV fieldnames for {csv_filename} could not be determined. Skipping write.")
             return

        # Open file in append mode, create writer if it doesn't exist for this file
        # Re-opening and closing per write is not efficient but robust for long scripts
        # For higher performance, keep file open, but that needs careful management.
        is_new_file = not os.path.exists(csv_filename) or os.path.getsize(csv_filename) == 0

        with open(csv_filename, 'a', newline='', encoding='utf-8') as csvfile:
            current_writer = csv.DictWriter(csvfile, fieldnames=writer_info['fieldnames'], extrasaction='ignore')
            if is_new_file or not get_csv_fieldnames_from_file(csv_filename): # Write header if new or empty
                current_writer.writeheader()

            current_writer.writerow(processed_data)

    except Exception as e:
        print(f"Error writing to CSV {csv_filename}: {e}")
        print(f"Problematic data: {processed_data}")

def make_api_request(limit, offset, cookies_dict, current_event_id):
    """Makes a request to the user list API endpoint and returns JSON data or None."""
    params = {
        'limit': limit,
        'offset': offset
    }
    url = f"{BASE_URL}?{urlencode(params)}"

    request_headers = HEADERS.copy()
    request_headers['user-agent'] = random.choice(USER_AGENTS) # Rotate User-Agent
    request_headers['referer'] = f'https://globalfinance.connectmeinforma.com/{current_event_id}/attendees'

    # Initial delay before the first attempt for this offset (outside retry loop)
    # This ensures a base politeness delay between fetching different offsets.
    time.sleep(random.uniform(5, 10))

    retries = 0
    while retries <= MAX_RETRIES_ON_RATE_LIMIT:
        try:
            print(f"Fetching users: offset={offset}, limit={limit} (Attempt {retries + 1}/{MAX_RETRIES_ON_RATE_LIMIT + 1})")
            response = requests.get(url, headers=request_headers, cookies=cookies_dict, timeout=REQUEST_TIMEOUT)

            if response.status_code == 200:
                try:
                    return response.json()
                except json.JSONDecodeError:
                    print(f"  [!] JSONDecodeError for offset {offset}. Response text: {response.text[:200]}...")
                    return None # Treat as a content error for this request

            elif response.status_code == 429: # Too Many Requests
                retries += 1
                if retries > MAX_RETRIES_ON_RATE_LIMIT:
                    print(f"  [!] Max retries ({MAX_RETRIES_ON_RATE_LIMIT}) reached for offset {offset} due to 429 errors. Aborting this request.")
                    return None

                backoff_time = (INITIAL_BACKOFF_SECONDS * (2 ** (retries - 1))) + random.uniform(0, INITIAL_BACKOFF_SECONDS * 0.2)
                print(f"  [!] HTTP 429 (Too Many Requests) for offset {offset}. Retrying in {backoff_time:.2f} seconds...")
                time.sleep(backoff_time)
                continue # Go to next iteration of the while loop (retry)

            elif response.status_code == 403: # Forbidden
                print(f"  [!] HTTP 403 (Forbidden) for offset {offset}. This may indicate a block. Response: {response.text[:200]}...")
                return {"error": "forbidden", "status_code": 403} # Special marker for main loop to handle

            else: # Other non-200, non-429, non-403 errors
                print(f"  [!] HTTP {response.status_code} for offset {offset}. Response: {response.text[:200]}...")
                return None # Indicate error for this request

        except requests.exceptions.RequestException as e:
            print(f"  [!] RequestException for offset {offset}: {e}")
            # For simplicity, general request exceptions are not retried here.
            # Could add specific retry logic for timeouts if desired.
            return None # Indicate error for this request

    # If loop finishes, it means all retries for 429 were exhausted
    print(f"  [!] Failed to fetch offset {offset} after {MAX_RETRIES_ON_RATE_LIMIT + 1} attempts due to persistent 429 errors.")
    return None

def load_checkpoint():
    if os.path.exists(CHECKPOINT_FILE):
        try:
            with open(CHECKPOINT_FILE, 'r') as f:
                offset = int(f.read().strip())
                print(f"Resuming from checkpoint. Offset: {offset}")
                return offset
        except ValueError:
            print(f"Warning: Invalid content in {CHECKPOINT_FILE}. Starting from offset 0.")
        except Exception as e:
            print(f"Warning: Could not read {CHECKPOINT_FILE}: {e}. Starting from offset 0.")
    return 0

def save_checkpoint(offset):
    try:
        with open(CHECKPOINT_FILE, 'w') as f:
            f.write(str(offset))
    except Exception as e:
        print(f"Error saving checkpoint to {CHECKPOINT_FILE}: {e}")

# --- Main Script Logic ---
def main():
    print("Starting User Scraper (Offset-based)...")

    parsed_cookies = parse_cookies(COOKIE_STRING)
    if not parsed_cookies:
        print("ERROR: Cookie string is empty or could not be parsed. Please check COOKIE_STRING.")
        return

    # Validate eid in cookie against EVENT_ID (optional, but good practice)
    if 'eid' not in parsed_cookies or parsed_cookies.get('eid') != EVENT_ID:
        cookie_eid = parsed_cookies.get('eid', 'NOT_FOUND')
        print(f"WARNING: EVENT_ID '{EVENT_ID}' might mismatch cookie's eid ('{cookie_eid}'). Ensure cookie is for the correct event.")
        # Potentially update parsed_cookies['eid'] = EVENT_ID if script should enforce it

    current_offset = load_checkpoint()
    total_api_count = None # To store total count from API if available
    session_users_count = 0

    csv_fieldnames = None
    # Determine if we need to write headers
    # Similar logic to attendee_scraper.py
    write_header_flag = not (os.path.exists(RESULTS_CSV) and os.path.getsize(RESULTS_CSV) > 0 and get_csv_fieldnames_from_file(RESULTS_CSV))
    if not write_header_flag:
        csv_fieldnames = get_csv_fieldnames_from_file(RESULTS_CSV) # Try to load existing headers
        if csv_fieldnames:
             print(f"Appending to existing {RESULTS_CSV}. Headers: {csv_fieldnames}")
        else: # File exists but headers couldn't be read (e.g. empty or corrupted)
            print(f"Warning: {RESULTS_CSV} exists but headers unreadable. Will try to determine from data and overwrite if empty or start new.")
            write_header_flag = True # Force header write
            # Consider if we should delete/rename the old file if headers are mission critical for append.
            # For now, if it was empty, new headers will be written. If it had content and unreadable headers, DictWriter might misbehave.

    print(f"Initial offset: {current_offset}. Will write CSV header: {write_header_flag}")

    with open(RESULTS_CSV, 'a' if not write_header_flag else 'w', newline='', encoding='utf-8') as csvfile:
        csv_writer = None

        while True:
            api_data = make_api_request(LIMIT, current_offset, parsed_cookies, EVENT_ID)

            if api_data and isinstance(api_data, dict) and api_data.get("error") == "forbidden":
                print(f"CRITICAL: Received HTTP {api_data.get('status_code', 'Unknown')} (Forbidden/Blocked).")
                print("The script will stop to avoid further issues or detection.")
                print("Consider checking your IP, cookies, headers, or pausing scraping for a significant duration before retrying.")
                save_checkpoint(current_offset) # Save where we left off
                break # Exit the main while loop

            if not api_data:
                print("No data received from API (or max retries exceeded/other error). Assuming end of users for now or persistent issue.")
                # Consider if we should break or try again after a longer pause for some non-403/429 errors.
                # For now, we break, assuming the error handling in make_api_request was sufficient.
                save_checkpoint(current_offset) # Save progress before exiting
                break

            users = api_data.get("rows", [])

            if total_api_count is None and "count" in api_data:
                try:
                    total_api_count = int(api_data["count"])
                    print(f"API indicates a total of {total_api_count} users.")
                except ValueError:
                    print(f"Warning: Could not parse total count from API: {api_data['count']}")
                    total_api_count = 0

            if not users:
                if current_offset == 0 and total_api_count == 0 : # No users at all
                     print("No users found at all.")
                elif total_api_count is not None and current_offset >= total_api_count:
                    print("Reached or exceeded total user count indicated by API.")
                else:
                    print("No more users found in this batch.")
                save_checkpoint(current_offset)
                break

            if csv_writer is None:
                if not csv_fieldnames: # Not read from existing file, or write_header_flag forced re-evaluation
                    if users:
                        csv_fieldnames = list(users[0].keys()) # Determine from first user object
                        print(f"Determined CSV fieldnames from first fetched batch: {csv_fieldnames}")
                    else: # Should be caught by 'if not users:' above
                        print("CRITICAL: No users in first batch and no existing headers. Cannot proceed.")
                        return

                if not csv_fieldnames:
                     print("CRITICAL: CSV fieldnames could not be determined. Aborting.")
                     return

                csv_writer = csv.DictWriter(csvfile, fieldnames=csv_fieldnames, extrasaction='ignore')
                if write_header_flag:
                    print(f"Writing CSV header: {csv_fieldnames}")
                    csv_writer.writeheader()
                    write_header_flag = False # Header now written

            batch_processed_count = 0
            for user_data_item in users:
                processed_item = preprocess_data_for_csv(user_data_item)
                csv_writer.writerow(processed_item)
                batch_processed_count += 1

            session_users_count += batch_processed_count
            csvfile.flush() # Ensure data is written to disk after each batch

            current_offset += batch_processed_count # Correctly increment offset by number of items actually processed

            progress_message = f"Fetched and wrote {batch_processed_count} users. Total this session: {session_users_count}. Next offset: {current_offset}."
            if total_api_count is not None and total_api_count > 0:
                # current_offset is now the start of the *next* batch, so it represents items processed so far
                percentage = (current_offset / total_api_count) * 100
                etr_seconds = ((total_api_count - current_offset) / LIMIT) * AVERAGE_REQUEST_DELAY if total_api_count > current_offset else 0
                progress_message += f" Progress: {current_offset}/{total_api_count} ({percentage:.2f}%). Est. Time Rem: {format_time(etr_seconds)}."
            print(progress_message)

            save_checkpoint(current_offset)

            # If last batch was smaller than LIMIT, it's the end
            if batch_processed_count < LIMIT:
                print("Last batch was smaller than limit, assuming end of users.")
                break

    print(f"\nScript finished. Total users written this session: {session_users_count}")
    final_offset = load_checkpoint() # load_checkpoint will print the final offset
    if total_api_count is not None:
        print(f"Final offset is {final_offset}. API indicated total of {total_api_count} users.")


if __name__ == "__main__":
    if "YOUR_COOKIE_STRING_HERE" in COOKIE_STRING or len(COOKIE_STRING.strip()) < 50 : # Basic check
         print("ERROR: Please replace COOKIE_STRING in the script with your actual valid cookie.")
         print("The current cookie string is placeholder-like or too short.")
    else:
        main()
    print("User scraping process (offset-based) has concluded.")
